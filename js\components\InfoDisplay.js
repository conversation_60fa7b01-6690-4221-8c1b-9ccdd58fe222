/**
 * 信息展示组件
 */
class InfoDisplay {
  constructor(containerId, data) {
    this.container = document.getElementById(containerId);
    this.data = data;
    this.init();
  }

  init() {
    if (!this.container) {
      console.error('容器元素不存在');
      return;
    }

    this.render();
  }

  render() {
    // 获取当班费用和实时ACE的容器
    const costValueElement = this.container.querySelector('.info-box:nth-child(1) .info-box-value');
    const aceValueElement = this.container.querySelector('.info-box:nth-child(2) .info-box-value');
    
    if (costValueElement && aceValueElement) {
      costValueElement.textContent = this.data.currentCost;
      aceValueElement.textContent = this.data.realTimeACE;
    } else {
      console.error('信息显示元素不存在');
    }
  }

  // 更新数据
  updateData(newData) {
    this.data = newData;
    this.render();
  }
}

export default InfoDisplay;