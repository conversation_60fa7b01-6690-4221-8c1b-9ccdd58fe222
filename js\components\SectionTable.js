/**
 * 断面监视表格组件
 */
import { API_CONFIG } from "../config/ChartConfig.js";

class SectionTable {
  constructor(containerId, data = null) {
    this.container = document.getElementById(containerId);
    this.data = data;
    this.apiUrl = `${API_CONFIG.BASE_URL}${API_CONFIG.SECTION_DATA.url}`;
    this.init();
  }

  init() {
    if (!this.container) {
      console.error("容器元素不存在");
      return;
    }

    // 如果没有传入数据，则从接口获取
    if (!this.data) {
      this.fetchData();
    } else {
      this.render();
    }
  }

  // 从接口获取数据
  async fetchData() {
    try {
      const response = await fetch(this.apiUrl);
      const result = await response.json();

      if (result.code === "0000" && result.data) {
        // 转换接口数据为表格格式
        this.data = this.transformApiData(result.data);
        this.render();
      } else {
        console.error("接口返回错误:", result);
        this.showError("数据获取失败");
      }
    } catch (error) {
      console.error("接口调用失败:", error);
      this.showError("网络请求失败");
    }
  }

  // 转换接口数据为表格格式
  transformApiData(apiData) {
    const columns = [
      "断面名称",
      "电压(kV)",
      "限值(MW)",
      "实际值(MW)",
      "发生时间",
      "最大值(MW)",
      "差值(MW)",
    ];

    const data = apiData.map((item) => [
      item.sectionName,
      item.volt,
      item.limitValue,
      item.actualValue,
      item.occurTime,
      item.maxValue,
      item.diffValue,
    ]);

    return { columns, data };
  }

  // 显示错误信息
  showError(message) {
    this.container.innerHTML = `<div class="error-message">${message}</div>`;
  }

  render() {
    if (!this.data || !this.data.columns || !this.data.data) {
      this.showError("数据格式错误");
      return;
    }

    const { columns, data } = this.data;

    // 创建表格元素
    const table = document.createElement("table");
    table.className = "section-table";

    // 创建表头
    const thead = document.createElement("thead");
    const headerRow = document.createElement("tr");

    columns.forEach((column) => {
      const th = document.createElement("th");
      th.textContent = column;
      headerRow.appendChild(th);
    });

    thead.appendChild(headerRow);
    table.appendChild(thead);

    // 创建表格主体
    const tbody = document.createElement("tbody");

    data.forEach((row) => {
      const tr = document.createElement("tr");

      row.forEach((cell) => {
        const td = document.createElement("td");
        td.textContent = cell;
        tr.appendChild(td);
      });

      tbody.appendChild(tr);
    });

    table.appendChild(tbody);

    // 清空容器并添加表格
    this.container.innerHTML = "";
    this.container.appendChild(table);
  }

  // 更新数据
  updateData(newData) {
    this.data = newData;
    this.render();
  }

  // 刷新数据（重新从接口获取）
  refreshData() {
    this.fetchData();
  }
}

export default SectionTable;
