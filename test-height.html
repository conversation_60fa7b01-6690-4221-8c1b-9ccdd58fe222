<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>平衡曲线高度测试</title>
    <link rel="stylesheet" href="./css/style.css" />
    <style>
      /* 测试不同高度的容器 */
      .test-container {
        height: 400px;
        border: 2px solid #00aeff;
        margin: 20px;
      }
      .test-container-large {
        height: 600px;
        border: 2px solid #ff6600;
        margin: 20px;
      }
      .test-info {
        color: #fff;
        text-align: center;
        padding: 10px;
        background-color: #1a3a66;
        margin: 20px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="test-info">
        <h2>平衡曲线高度自适应测试</h2>
        <p>蓝色边框容器高度: 400px | 橙色边框容器高度: 600px</p>
      </div>
      
      <!-- 测试容器1: 400px高度 -->
      <div class="test-container">
        <div class="chart-container balance-chart" style="height: 100%; width: 100%;">
          <div class="chart-title">平衡曲线 (400px容器)</div>
          <div id="balance-chart-1" class="chart"></div>
        </div>
      </div>
      
      <!-- 测试容器2: 600px高度 -->
      <div class="test-container-large">
        <div class="chart-container balance-chart" style="height: 100%; width: 100%;">
          <div class="chart-title">平衡曲线 (600px容器)</div>
          <div id="balance-chart-2" class="chart"></div>
        </div>
      </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script type="module">
      import BalanceChart from "./js/components/BalanceChart.js";
      import * as data from "./js/data.js";

      // 页面加载完成后初始化图表
      document.addEventListener("DOMContentLoaded", () => {
        // 初始化第一个图表
        const balanceChart1 = new BalanceChart("balance-chart-1", data.balanceData);
        
        // 初始化第二个图表
        const balanceChart2 = new BalanceChart("balance-chart-2", data.balanceData);
        
        console.log("测试图表初始化完成");
      });
    </script>
  </body>
</html>
